"""
Timestamp Validator - Basic timestamp validation

This module provides validation functions for checking timestamp continuity
and identifying missing timestamps in data ranges.
"""

import pandas as pd
from app.core.helpers.convert_timeframe_to_seconds import convert_timeframe_to_seconds
from app.logger.get_logger import log


@log
def get_missing_timestamps(dataframe: pd.DataFrame, expected_interval: str, oldest_timestamp: int = None,
                           newest_timestamp: int = None) -> list:
    """
    Retrieves a list of timestamps that are missing from the given DataFrame.

    Parameters
    ----------
    dataframe : pd.DataFrame
        df containing a 'timestamp' column.
    expected_interval : str
        Expected interval between timestamps in the DataFrame (e.g. '1m', '1h', '1d').
    oldest_timestamp : int, optional
        The oldest timestamp to consider. If not specified, will use the oldest timestamp in the DataFrame.
    newest_timestamp : int, optional
        The newest timestamp to consider. If not specified, will use the newest timestamp in the DataFrame.

    Returns
    -------
    list
        A list of missing timestamps.

    Raises
    ------
    ValueError
        If the DataFrame is empty, or does not contain a 'timestamp' column, or if the 'timestamp' column contains invalid values.
    """
    try:
        dataframe['timestamp'] = pd.to_numeric(dataframe['timestamp'])
    except ValueError as e:
        raise ValueError("Invalid timestamp values in the DataFrame") from e

    if oldest_timestamp is None:
        try:
            oldest_timestamp = dataframe['timestamp'].nsmallest(1).iloc[0]
        except IndexError as e:
            raise ValueError("DataFrame is empty") from e
    if newest_timestamp is None:
        try:
            newest_timestamp = dataframe['timestamp'].nlargest(1).iloc[0]
        except IndexError as e:
            raise ValueError("DataFrame is empty") from e

    try:
        timestamps = dataframe[(dataframe['timestamp'] >= oldest_timestamp) \
                               & (dataframe['timestamp'] <= newest_timestamp)]['timestamp']
    except KeyError as e:
        raise ValueError("DataFrame does not contain a 'timestamp' column") from e

    missing_timestamps = []

    try:
        expected_diff = convert_timeframe_to_seconds(expected_interval)
    except ValueError as e:
        raise ValueError("Invalid expected interval") from e

    current_timestamp = oldest_timestamp

    while current_timestamp <= newest_timestamp:
        if current_timestamp not in timestamps.values:
            missing_timestamps.append(current_timestamp)
        current_timestamp += expected_diff

    return missing_timestamps

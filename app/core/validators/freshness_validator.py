"""
Freshness Validator - Basic data freshness validation

This module provides validation for checking if data is fresh enough
based on timestamp analysis for cache validation.
"""

import pandas as pd
from app.logger.get_logger import log


@log
def verify_data_freshness(df, timeframe: str, api_call_timestamp: int):
    """Verify if the data in the given dataframe is fresh based on the given timeframe.

    The freshness is determined by comparing the newest timestamp in the dataframe
    with the current time minus the timeframe duration.

    Args:
    - df (pd.DataFrame): The dataframe to check.
    - timeframe (str): The timeframe to use for the check.
    - api_call_timestamp (int): The timestamp when the API call was made.

    Returns:
    - bool: True if the data is fresh, False otherwise.
    """
    timeframe_multiplier = {
            "m1" : 60,
            "m5" : 300,
            "m15": 900,
            "h1" : 3600,
            "h4" : 14400,
            "d1" : 86400
    }

    newest_timestamp = int(df.timestamp.max())
    now_utc = api_call_timestamp
    timeframe_duration = timeframe_multiplier[timeframe]
    diff = now_utc - newest_timestamp
    diff = diff - timeframe_duration  # buffer because when exclude current candle is enabled -> always fails

    return diff <= timeframe_duration

"""
Data Completeness Validator - Basic data completeness validation

This module provides simple validation for checking if data contains the
expected number of candles and has continuous timestamps.
"""

import pandas as pd
from app.logger.get_logger import log


@log
def is_data_complete(df, expected_klines):
    """
    Checks if the given pandas DataFrame `df` contains `expected_klines` number of klines,
    and that the timestamps are continuous.

    Parameters
    ----------
    df : pandas DataFrame
        The DataFrame to check.
    expected_klines : str / int
        The number of klines expected in the DataFrame.

    Returns
    -------
    bool
        True if the DataFrame contains the expected number of klines and the timestamps
        are continuous, False otherwise.
    """
    df_rows = len(df)
    expected_klines = int(expected_klines)
    if df_rows == 1 and expected_klines == 1:
        return True

    if df is None or df_rows < expected_klines:
        return False

    # Convert the timestamp column to a numeric type
    df['timestamp'] = pd.to_numeric(df['timestamp'], errors='coerce')

    timestamps = df['timestamp']
    if not (timestamps.diff().dropna().eq(timestamps.diff().dropna().iloc[0])).all():
        return False

    return True

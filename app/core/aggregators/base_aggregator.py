"""
Base Aggregator - Abstract base class for all data aggregators

This module defines the interface and common functionality for all data aggregators
in the dabot-ohlc system. It provides a consistent API for combining data from
multiple sources into unified datasets.

Key Responsibilities:
1. Define the standard interface for all aggregators
2. Provide common utility methods for data processing
3. Implement base quality assessment functionality
4. Handle common error scenarios
5. Provide logging and monitoring capabilities

Design Patterns:
- Template Method: Define algorithm structure, let subclasses implement details
- Strategy Pattern: Allow different aggregation strategies
- Observer Pattern: Notify about quality issues

Quality Assessment Framework:
- Completeness: Check for missing data points
- Consistency: Compare values across sources
- Timeliness: Validate data freshness
- Reasonableness: Detect obvious outliers

Error Handling:
- Graceful degradation when sources provide poor data
- Fallback to single source when aggregation fails
- Comprehensive error logging and reporting
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta

from app.logger.get_logger import log, logger


class BaseAggregator:
    """
    Abstract base class for all data aggregators.

    This class defines the common interface and provides shared functionality
    for aggregating data from multiple sources into unified datasets.
    """

    def __init__(self, aggregation_strategy: str = 'equal_weight'):
        """
        Initialize the base aggregator.

        Args:
            aggregation_strategy: Strategy to use for aggregation
                                ('equal_weight', 'volume_weighted', 'simple_average')
        """
        from app.config import QUALITY_THRESHOLDS

        self.aggregation_strategy = aggregation_strategy
        self.quality_thresholds = QUALITY_THRESHOLDS
        self.logger = logger

    def aggregate(self, source_data: Dict[str, pd.DataFrame],
                 metadata: Dict[str, Dict]) -> Tuple[pd.DataFrame, Dict]:
        """
        Main aggregation method - template method pattern.

        This method defines the overall aggregation algorithm:
        1. Validate input data
        2. Preprocess data from each source
        3. Align timestamps across sources
        4. Apply aggregation strategy
        5. Assess quality of result
        6. Post-process aggregated data

        Args:
            source_data: Dict mapping source names to DataFrames
            metadata: Dict mapping source names to metadata dicts

        Returns:
            Tuple of (aggregated_dataframe, aggregation_metadata)
        """
        try:
            # Step 1: Preprocess data first (handles data type conversion)
            if not source_data:
                return pd.DataFrame(), {"error": "No source data provided"}

            processed_data = self._preprocess_source_data(source_data)
            if not processed_data:
                return pd.DataFrame(), {"error": "No data after preprocessing"}

            # Step 2: Validate preprocessed data
            valid_sources = self._validate_input_data(processed_data)
            if not valid_sources:
                return pd.DataFrame(), {"error": "No valid source data after preprocessing"}

            # Step 3: Use only valid sources for aggregation
            valid_processed_data = {source: processed_data[source] for source in valid_sources}

            # Step 4: Align timestamps across sources
            aligned_data = self._align_timestamps(valid_processed_data)

            # Step 5: Apply aggregation strategy
            aggregated_df = self._apply_aggregation_strategy(aligned_data)

            # Step 6: Get aggregation metadata (no quality assessment)
            aggregation_metadata = self._get_aggregation_metadata(aggregated_df, valid_processed_data)

            # Step 7: Post-process aggregated data
            final_df = self._post_process_data(aggregated_df, aggregation_metadata)

            return final_df, aggregation_metadata

        except Exception as e:
            self.logger.error(f"Error in aggregation: {str(e)}")
            return pd.DataFrame(), {"error": str(e)}

    def _validate_input_data(self, source_data: Dict[str, pd.DataFrame]) -> List[str]:
        """
        Validate input data from all sources.

        Checks:
        - Data format consistency
        - Required columns presence
        - Data type validation
        - Basic sanity checks

        Args:
            source_data: Dict mapping source names to DataFrames

        Returns:
            List of valid source names
        """
        valid_sources = []
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

        for source, df in source_data.items():
            try:
                # Check if DataFrame is not empty
                if df.empty:
                    self.logger.warning(f"Empty DataFrame from source: {source}")
                    continue

                # Check required columns
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    self.logger.warning(f"Missing columns in {source}: {missing_columns}")
                    continue

                # Check data types
                if not pd.api.types.is_numeric_dtype(df['timestamp']):
                    self.logger.warning(f"Invalid timestamp type in {source}")
                    continue

                # Check for reasonable OHLC values
                numeric_cols = ['open', 'high', 'low', 'close', 'volume']
                for col in numeric_cols:
                    if not pd.api.types.is_numeric_dtype(df[col]):
                        self.logger.warning(f"Invalid {col} type in {source}")
                        break

                    if col != 'volume' and (df[col] <= 0).any():  # Prices should be positive
                        self.logger.warning(f"Non-positive {col} values in {source}")
                        break
                else:
                    # Check OHLC relationships (more lenient - only check high >= low)
                    invalid_ohlc = (df['high'] < df['low'])

                    if invalid_ohlc.any():
                        self.logger.warning(f"Invalid OHLC relationships in {source} (high < low)")
                        continue

                    valid_sources.append(source)
                    self.logger.debug(f"Validated source: {source} with {len(df)} records")

            except Exception as e:
                self.logger.error(f"Error validating {source}: {str(e)}")
                continue

        return valid_sources

    def _align_timestamps(self, source_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Basic timestamp alignment - just return data as-is for base implementation."""
        return source_data

    def _apply_aggregation_strategy(self, aligned_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Apply true multi-source aggregation strategy.

        Aggregates data from all available sources using equal weighting.
        """
        if not aligned_data:
            return pd.DataFrame()

        if len(aligned_data) == 1:
            # Single source - return as-is
            return list(aligned_data.values())[0]

        # Multi-source aggregation
        return self._merge_multiple_sources(aligned_data)

    def _get_aggregation_metadata(self, aggregated_data: pd.DataFrame,
                                 source_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Get basic metadata about the aggregation process (no quality assessment).
        """
        try:
            return {
                "source_count": len(source_data),
                "aggregation_method": self.aggregation_strategy,
                "data_count": len(aggregated_data) if not aggregated_data.empty else 0,
                "sources_used": list(source_data.keys())
            }
        except Exception as e:
            self.logger.error(f"Error getting aggregation metadata: {str(e)}")
            return {
                "source_count": len(source_data) if source_data else 0,
                "aggregation_method": self.aggregation_strategy,
                "data_count": 0,
                "sources_used": []
            }

    def _post_process_data(self, aggregated_data: pd.DataFrame,
                          aggregation_metadata: Dict[str, Any]) -> pd.DataFrame:
        """Basic post-processing - just return data as-is."""
        return aggregated_data

    def _preprocess_source_data(self, source_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Preprocess data from each source before aggregation.

        Common preprocessing steps:
        - Data type conversion
        - Timestamp normalization
        - Outlier detection and handling
        - Missing value handling

        Args:
            source_data: Raw data from sources

        Returns:
            Dict of preprocessed DataFrames
        """
        processed_data = {}

        for source, df in source_data.items():
            try:
                if df.empty:
                    self.logger.warning(f"Empty DataFrame from {source}")
                    continue

                # Create a copy to avoid modifying original data
                processed_df = df.copy()

                # Convert data types for OHLC columns
                numeric_columns = ['open', 'high', 'low', 'close', 'volume']
                for col in numeric_columns:
                    if col in processed_df.columns:
                        try:
                            # Convert to numeric, handling strings and other types
                            processed_df[col] = pd.to_numeric(processed_df[col], errors='coerce')

                            # Check for NaN values after conversion
                            if processed_df[col].isna().any():
                                self.logger.warning(f"NaN values found in {col} for {source} after type conversion")
                                # Fill NaN with forward fill, then backward fill
                                processed_df[col] = processed_df[col].ffill().bfill()

                        except Exception as e:
                            self.logger.error(f"Error converting {col} to numeric for {source}: {str(e)}")
                            continue

                # Ensure timestamp is numeric
                if 'timestamp' in processed_df.columns:
                    try:
                        processed_df['timestamp'] = pd.to_numeric(processed_df['timestamp'], errors='coerce')
                        if processed_df['timestamp'].isna().any():
                            self.logger.warning(f"Invalid timestamps found in {source}")
                            # Remove rows with invalid timestamps
                            processed_df = processed_df.dropna(subset=['timestamp'])
                    except Exception as e:
                        self.logger.error(f"Error converting timestamp for {source}: {str(e)}")
                        continue

                # Basic validation: ensure positive prices
                price_columns = ['open', 'high', 'low', 'close']
                for col in price_columns:
                    if col in processed_df.columns:
                        invalid_prices = processed_df[col] <= 0
                        if invalid_prices.any():
                            self.logger.warning(f"Non-positive {col} values found in {source}")
                            # Remove rows with invalid prices
                            processed_df = processed_df[~invalid_prices]

                # Ensure volume is non-negative
                if 'volume' in processed_df.columns:
                    invalid_volume = processed_df['volume'] < 0
                    if invalid_volume.any():
                        self.logger.warning(f"Negative volume values found in {source}")
                        processed_df.loc[invalid_volume, 'volume'] = 0

                # Final check: ensure we still have data
                if not processed_df.empty:
                    processed_data[source] = processed_df
                    self.logger.debug(f"Successfully preprocessed {len(processed_df)} records from {source}")
                else:
                    self.logger.warning(f"No valid data remaining from {source} after preprocessing")

            except Exception as e:
                self.logger.error(f"Error preprocessing data from {source}: {str(e)}")
                continue

        return processed_data

    def _merge_multiple_sources(self, source_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Merge data from multiple sources using equal weighting.

        Args:
            source_data: Dict mapping source names to DataFrames

        Returns:
            DataFrame with aggregated data from all sources
        """
        try:
            if not source_data:
                return pd.DataFrame()

            # Get all unique timestamps across all sources
            all_timestamps = set()
            for df in source_data.values():
                if not df.empty and 'timestamp' in df.columns:
                    all_timestamps.update(df['timestamp'].tolist())

            if not all_timestamps:
                return pd.DataFrame()

            # Sort timestamps
            sorted_timestamps = sorted(all_timestamps)

            # Initialize result DataFrame
            result_data = []

            for timestamp in sorted_timestamps:
                # Collect data from all sources for this timestamp
                timestamp_data = {}
                contributing_sources = []

                for source_name, df in source_data.items():
                    source_row = df[df['timestamp'] == timestamp]
                    if not source_row.empty:
                        timestamp_data[source_name] = source_row.iloc[0]
                        contributing_sources.append(source_name)

                if not timestamp_data:
                    continue

                # Aggregate OHLC values using equal weighting (arithmetic mean)
                aggregated_row = self._aggregate_timestamp_data(timestamp_data, contributing_sources)
                if aggregated_row is not None:
                    result_data.append(aggregated_row)

            if not result_data:
                return pd.DataFrame()

            # Create result DataFrame
            result_df = pd.DataFrame(result_data)

            # Sort by timestamp descending (most recent first)
            result_df = result_df.sort_values('timestamp', ascending=False).reset_index(drop=True)

            return result_df

        except Exception as e:
            self.logger.error(f"Error merging multiple sources: {str(e)}")
            return pd.DataFrame()

    def _aggregate_timestamp_data(self, timestamp_data: Dict[str, pd.Series],
                                 contributing_sources: List[str]) -> Dict[str, Any]:
        """
        Aggregate data from multiple sources for a single timestamp.

        Args:
            timestamp_data: Dict mapping source names to data rows
            contributing_sources: List of sources that have data for this timestamp

        Returns:
            Dict with aggregated values for this timestamp
        """
        try:
            if not timestamp_data:
                return None

            # Get timestamp and date (should be same across sources)
            first_source = list(timestamp_data.keys())[0]
            timestamp = timestamp_data[first_source]['timestamp']
            date = timestamp_data[first_source]['date']

            # Aggregate OHLC values using arithmetic mean
            ohlc_values = {'open': [], 'high': [], 'low': [], 'close': []}
            volumes = []

            for source_name, row in timestamp_data.items():
                try:
                    ohlc_values['open'].append(float(row['open']))
                    ohlc_values['high'].append(float(row['high']))
                    ohlc_values['low'].append(float(row['low']))
                    ohlc_values['close'].append(float(row['close']))
                    volumes.append(float(row['volume']))
                except (ValueError, KeyError) as e:
                    self.logger.warning(f"Invalid data from {source_name} for timestamp {timestamp}: {e}")
                    continue

            if not ohlc_values['open']:  # No valid data
                return None

            # Calculate aggregated values
            aggregated_open = sum(ohlc_values['open']) / len(ohlc_values['open'])
            aggregated_high = sum(ohlc_values['high']) / len(ohlc_values['high'])
            aggregated_low = sum(ohlc_values['low']) / len(ohlc_values['low'])
            aggregated_close = sum(ohlc_values['close']) / len(ohlc_values['close'])
            aggregated_volume = sum(volumes)  # Sum volumes from all sources

            return {
                'timestamp': int(timestamp),
                'date': date,
                'open': aggregated_open,
                'high': aggregated_high,
                'low': aggregated_low,
                'close': aggregated_close,
                'volume': aggregated_volume,
                'source_count': len(contributing_sources),
                'contributing_sources': contributing_sources
            }

        except Exception as e:
            self.logger.error(f"Error aggregating timestamp data: {str(e)}")
            return None

    def _post_process(self, aggregated_data: pd.DataFrame) -> pd.DataFrame:
        """
        Post-process aggregated data before returning.

        Final cleanup steps:
        - Sort by timestamp
        - Remove duplicates
        - Apply final validation
        - Format for output

        Args:
            aggregated_data: Raw aggregated data

        Returns:
            Final processed DataFrame

        TODO: Implement post-processing pipeline
        """
        pass

    # Removed quality assessment methods - no quality assessment needed

    def get_supported_strategies(self) -> List[str]:
        """
        Get list of supported aggregation strategies.

        Returns:
            List of strategy names
        """
        pass
